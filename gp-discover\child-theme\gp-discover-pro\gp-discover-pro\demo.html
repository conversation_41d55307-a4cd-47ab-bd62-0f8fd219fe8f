<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GP Discover Pro Performance - Demo</title>
    <style>
        /* Performance-First CSS - Inline Critical Styles */
        :root {
            --primary: #4f46e5;
            --primary-dark: #3730a3;
            --text: #111827;
            --text-light: #6b7280;
            --bg: #ffffff;
            --bg-alt: #f9fafb;
            --border: #e5e7eb;
            --success: #10b981;
            --font: system-ui, -apple-system, sans-serif;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --container: 1200px;
            --radius: 0.5rem;
            --shadow: 0 1px 3px rgba(0,0,0,0.1);
            --duration: 200ms;
            --ease: ease-out;
        }

        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        body {
            font-family: var(--font);
            background: var(--bg);
            color: var(--text);
            line-height: 1.5;
            overflow-x: hidden;
        }

        .container {
            max-width: var(--container);
            margin: 0 auto;
            padding: 0 var(--space-4);
        }

        /* Navigation - Performance Critical */
        .main-navigation {
            background: var(--bg);
            border-bottom: 1px solid var(--border);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: var(--space-4) 0;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: var(--text-xl);
            font-weight: 600;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: var(--space-6);
            list-style: none;
        }

        .nav-links a {
            color: var(--text);
            text-decoration: none;
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius);
            transition: color var(--duration) var(--ease);
        }

        .nav-links a:hover {
            color: var(--primary);
        }

        /* Hero Section */
        .hero {
            padding: var(--space-8) 0;
            text-align: center;
            background: linear-gradient(135deg, var(--bg-alt) 0%, var(--bg) 100%);
        }

        .hero h1 {
            font-size: var(--text-3xl);
            font-weight: 700;
            margin-bottom: var(--space-4);
            color: var(--text);
        }

        .hero p {
            font-size: var(--text-lg);
            color: var(--text-light);
            margin-bottom: var(--space-6);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Performance Stats */
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-4);
            margin: var(--space-8) 0;
        }

        .stat-card {
            background: var(--bg);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: var(--space-6);
            text-align: center;
            box-shadow: var(--shadow);
        }

        .stat-number {
            font-size: var(--text-2xl);
            font-weight: 700;
            color: var(--success);
            margin-bottom: var(--space-2);
        }

        .stat-label {
            color: var(--text-light);
            font-size: var(--text-sm);
        }

        /* Cards */
        .card {
            background: var(--bg);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .card img {
            width: 100%;
            height: auto;
            display: block;
            aspect-ratio: 16/9;
            object-fit: cover;
        }

        .card-content {
            padding: var(--space-4);
        }

        .card-title {
            font-size: var(--text-lg);
            font-weight: 600;
            margin-bottom: var(--space-2);
        }

        .card-excerpt {
            color: var(--text-light);
            margin-bottom: var(--space-3);
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: var(--space-3) var(--space-6);
            background: var(--primary);
            color: white;
            text-decoration: none;
            border-radius: var(--radius);
            font-weight: 500;
            transition: background-color var(--duration) var(--ease);
        }

        .btn:hover {
            background: var(--primary-dark);
        }

        .btn-secondary {
            background: var(--bg-alt);
            color: var(--text);
            border: 1px solid var(--border);
        }

        .btn-secondary:hover {
            background: var(--border);
        }

        /* Grid */
        .grid {
            display: grid;
            gap: var(--space-4);
        }

        .grid-3 {
            grid-template-columns: repeat(3, 1fr);
        }

        @media (max-width: 768px) {
            .grid-3 {
                grid-template-columns: 1fr;
            }
            .nav-links {
                display: none;
            }
        }

        /* Performance Badge */
        .performance-badge {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--success);
            color: white;
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius);
            font-size: var(--text-sm);
            font-weight: 600;
            z-index: 1001;
            box-shadow: var(--shadow);
        }

        .section {
            padding: var(--space-8) 0;
        }

        .section-title {
            font-size: var(--text-2xl);
            font-weight: 700;
            text-align: center;
            margin-bottom: var(--space-6);
        }
    </style>
</head>
<body>
    <!-- Performance Badge -->
    <div class="performance-badge">
        ⚡ 95+ PageSpeed Score
    </div>

    <!-- Navigation -->
    <nav class="main-navigation">
        <div class="container">
            <div class="nav-content">
                <a href="#" class="logo">GP Discover Pro</a>
                <ul class="nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#features">Features</a></li>
                    <li><a href="#performance">Performance</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <h1>⚡ Core Web Vitals Optimized Theme</h1>
            <p>Performance-first WordPress child theme designed for speed, accessibility, and exceptional user experience. Achieve 95+ PageSpeed scores with minimal code.</p>
            <a href="#performance" class="btn">View Performance</a>
            <a href="#features" class="btn btn-secondary">Features</a>
        </div>
    </section>

    <!-- Performance Stats -->
    <section class="section" id="performance">
        <div class="container">
            <h2 class="section-title">🎯 Core Web Vitals Achievement</h2>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">< 2.5s</div>
                    <div class="stat-label">Largest Contentful Paint (LCP)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">< 100ms</div>
                    <div class="stat-label">First Input Delay (FID)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">< 0.1</div>
                    <div class="stat-label">Cumulative Layout Shift (CLS)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">95+</div>
                    <div class="stat-label">PageSpeed Insights Score</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section class="section" id="features">
        <div class="container">
            <h2 class="section-title">🚀 Performance Features</h2>
            <div class="grid grid-3">
                <div class="card">
                    <div class="card-content">
                        <h3 class="card-title">⚡ Minimal CSS</h3>
                        <p class="card-excerpt">Only 400 lines of performance-optimized CSS vs 1600+ in complex themes.</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-content">
                        <h3 class="card-title">🚫 No jQuery</h3>
                        <p class="card-excerpt">Removed 85KB+ jQuery dependency for better FID performance.</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-content">
                        <h3 class="card-title">🎨 System Fonts</h3>
                        <p class="card-excerpt">Zero font loading time using device's native fonts.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Minimal JavaScript - Performance Optimized
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling for anchor links
            document.addEventListener('click', function(e) {
                const link = e.target.closest('a[href^="#"]');
                if (!link) return;
                
                const targetId = link.getAttribute('href').substring(1);
                const target = document.getElementById(targetId);
                if (!target) return;
                
                e.preventDefault();
                target.scrollIntoView({ behavior: 'smooth' });
            });
            
            console.log('⚡ GP Discover Pro Performance - Loaded in minimal time!');
        });
    </script>
</body>
</html>
