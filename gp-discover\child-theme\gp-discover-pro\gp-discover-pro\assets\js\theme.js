/**
 * GP Discover Pro Performance - Minimal JavaScript
 *
 * Core Web Vitals optimized JavaScript
 * Target: FID <100ms, minimal execution time
 *
 * @version 2.1.0
 * <AUTHOR>
 */

(function() {
    'use strict';

    // Performance monitoring - Core Web Vitals
    if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (entry.entryType === 'largest-contentful-paint') {
                    console.log('LCP:', entry.startTime);
                }
                if (entry.entryType === 'first-input') {
                    console.log('FID:', entry.processingStart - entry.startTime);
                }
                if (entry.entryType === 'layout-shift') {
                    if (!entry.hadRecentInput) {
                        console.log('CLS:', entry.value);
                    }
                }
            }
        });

        observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
    }

    // Minimal DOM ready
    const ready = (fn) => {
        document.readyState !== 'loading' ? fn() : document.addEventListener('DOMContentLoaded', fn);
    };

    // Essential lazy loading - Intersection Observer
    const initLazyLoading = () => {
        if (!('IntersectionObserver' in window)) return;

        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.classList.add('loaded');
                    imageObserver.unobserve(img);
                }
            });
        }, { rootMargin: '50px' });

        document.querySelectorAll('img[loading="lazy"]').forEach(img => {
            imageObserver.observe(img);
        });
    };

    // Minimal smooth scrolling - Respects motion preferences
    const initSmoothScrolling = () => {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (!link) return;

            const targetId = link.getAttribute('href').substring(1);
            const target = document.getElementById(targetId);
            if (!target) return;

            e.preventDefault();

            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
            target.scrollIntoView({
                behavior: prefersReducedMotion ? 'auto' : 'smooth',
                block: 'start'
            });
        });
    };

    // Skip link accessibility
    const initSkipLinks = () => {
        document.addEventListener('click', (e) => {
            const skipLink = e.target.closest('.skip-link');
            if (!skipLink) return;

            const targetId = skipLink.getAttribute('href').substring(1);
            const target = document.getElementById(targetId);
            if (target) {
                target.focus();
                target.scrollIntoView();
            }
        });
    };

    // Minimal navigation scroll effect
    const initNavigation = () => {
        const nav = document.querySelector('.main-navigation');
        if (!nav) return;

        let ticking = false;
        const updateNav = () => {
            nav.classList.toggle('scrolled', window.scrollY > 50);
            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateNav);
                ticking = true;
            }
        }, { passive: true });
    };

    // Initialize essential features only
    ready(() => {
        initLazyLoading();
        initSmoothScrolling();
        initSkipLinks();
        initNavigation();

        // Mark as loaded for CSS
        document.body.classList.add('js-loaded');

        console.log('GP Discover Performance - Initialized! ⚡');
    });

})();
