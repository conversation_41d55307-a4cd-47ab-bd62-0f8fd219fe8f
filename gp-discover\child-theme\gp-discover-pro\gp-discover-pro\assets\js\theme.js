/**
 * GP Discover Pro Ultra - Modern JavaScript
 * 
 * Ultra-modern, performance-optimized JavaScript for enhanced user experience
 * Features: Intersection Observer, Web APIs, Progressive Enhancement
 * 
 * @version 2.0.0
 * <AUTHOR>
 */

(function() {
    'use strict';

    // Performance monitoring
    const perfObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
            if (entry.entryType === 'largest-contentful-paint') {
                console.log('LCP:', entry.startTime);
            }
        }
    });

    if ('PerformanceObserver' in window) {
        perfObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    }

    // Modern DOM ready function
    const ready = (fn) => {
        if (document.readyState !== 'loading') {
            fn();
        } else {
            document.addEventListener('DOMContentLoaded', fn);
        }
    };

    // Enhanced Lazy Loading with Intersection Observer
    const initLazyLoading = () => {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        
                        // Load the image
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                        }
                        
                        // Add loaded class for animations
                        img.classList.add('loaded');
                        
                        // Stop observing this image
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Observe all lazy images
            document.querySelectorAll('img[loading="lazy"], img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    };

    // Smooth Scrolling for Anchor Links
    const initSmoothScrolling = () => {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    e.preventDefault();
                    
                    // Respect user's motion preferences
                    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
                    
                    targetElement.scrollIntoView({
                        behavior: prefersReducedMotion ? 'auto' : 'smooth',
                        block: 'start'
                    });
                    
                    // Update URL without jumping
                    if (history.pushState) {
                        history.pushState(null, null, `#${targetId}`);
                    }
                }
            });
        });
    };

    // Enhanced Navigation with Scroll Detection
    const initNavigationEnhancements = () => {
        const nav = document.querySelector('.main-navigation');
        if (!nav) return;

        let lastScrollY = window.scrollY;
        let ticking = false;

        const updateNavigation = () => {
            const scrollY = window.scrollY;
            
            // Add scrolled class for styling
            if (scrollY > 100) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }

            // Hide/show navigation on scroll (optional)
            if (Math.abs(scrollY - lastScrollY) > 10) {
                if (scrollY > lastScrollY && scrollY > 200) {
                    nav.style.transform = 'translateY(-100%)';
                } else {
                    nav.style.transform = 'translateY(0)';
                }
                lastScrollY = scrollY;
            }

            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateNavigation);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestTick, { passive: true });
    };

    // Modern Card Hover Effects with Performance Optimization
    const initCardEffects = () => {
        const cards = document.querySelectorAll('.card');
        
        cards.forEach(card => {
            let isHovering = false;
            
            card.addEventListener('mouseenter', () => {
                if (!isHovering) {
                    isHovering = true;
                    card.style.willChange = 'transform';
                }
            });
            
            card.addEventListener('mouseleave', () => {
                if (isHovering) {
                    isHovering = false;
                    card.style.willChange = 'auto';
                }
            });
        });
    };

    // Progressive Web App Features
    const initPWAFeatures = () => {
        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // Add to Home Screen Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // Show install button if needed
            const installButton = document.querySelector('.install-app-button');
            if (installButton) {
                installButton.style.display = 'block';
                installButton.addEventListener('click', () => {
                    deferredPrompt.prompt();
                    deferredPrompt.userChoice.then((choiceResult) => {
                        if (choiceResult.outcome === 'accepted') {
                            console.log('User accepted the A2HS prompt');
                        }
                        deferredPrompt = null;
                    });
                });
            }
        });
    };

    // Dark Mode Toggle with System Preference Detection
    const initDarkMode = () => {
        const darkModeToggle = document.querySelector('.dark-mode-toggle');
        if (!darkModeToggle) return;

        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
        const currentTheme = localStorage.getItem('theme');

        // Set initial theme
        if (currentTheme) {
            document.documentElement.setAttribute('data-theme', currentTheme);
        } else if (prefersDark.matches) {
            document.documentElement.setAttribute('data-theme', 'dark');
        }

        // Toggle functionality
        darkModeToggle.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        });

        // Listen for system preference changes
        prefersDark.addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
            }
        });
    };

    // Enhanced Form Handling
    const initFormEnhancements = () => {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                // Floating label effect
                const updateLabel = () => {
                    const label = form.querySelector(`label[for="${input.id}"]`);
                    if (label) {
                        if (input.value || input === document.activeElement) {
                            label.classList.add('active');
                        } else {
                            label.classList.remove('active');
                        }
                    }
                };

                input.addEventListener('focus', updateLabel);
                input.addEventListener('blur', updateLabel);
                input.addEventListener('input', updateLabel);
                
                // Initial check
                updateLabel();
            });
        });
    };

    // Accessibility Enhancements
    const initAccessibilityFeatures = () => {
        // Skip link functionality
        const skipLinks = document.querySelectorAll('.skip-link');
        skipLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const targetId = link.getAttribute('href').substring(1);
                const target = document.getElementById(targetId);
                if (target) {
                    target.focus();
                    target.scrollIntoView();
                }
            });
        });

        // Keyboard navigation for custom elements
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // Close any open modals or dropdowns
                const openElements = document.querySelectorAll('.is-open, .show');
                openElements.forEach(el => {
                    el.classList.remove('is-open', 'show');
                });
            }
        });
    };

    // Initialize all features when DOM is ready
    ready(() => {
        initLazyLoading();
        initSmoothScrolling();
        initNavigationEnhancements();
        initCardEffects();
        initPWAFeatures();
        initDarkMode();
        initFormEnhancements();
        initAccessibilityFeatures();

        // Add loaded class to body for CSS animations
        document.body.classList.add('js-loaded');
        
        console.log('GP Discover Pro Ultra - Theme initialized successfully! 🚀');
    });

    // Export functions for external use
    window.GPDiscoverUltra = {
        initLazyLoading,
        initSmoothScrolling,
        initNavigationEnhancements,
        initCardEffects,
        initDarkMode,
        initFormEnhancements,
        initAccessibilityFeatures
    };

})();
