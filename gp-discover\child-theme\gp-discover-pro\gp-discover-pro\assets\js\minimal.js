/**
 * GP Discover Pro Performance - Ultra Minimal JavaScript
 * 
 * Core Web Vitals optimized - FID <100ms target
 * Only essential functionality for performance
 * 
 * @version 2.1.0
 * <AUTHOR>
 */

(function() {
    'use strict';

    // Minimal DOM ready - Performance optimized
    const ready = (fn) => {
        document.readyState !== 'loading' ? fn() : document.addEventListener('DOMContentLoaded', fn);
    };

    // Essential image lazy loading
    const initImages = () => {
        if (!('IntersectionObserver' in window)) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('loaded');
                    observer.unobserve(entry.target);
                }
            });
        });

        document.querySelectorAll('img[loading="lazy"]').forEach(img => observer.observe(img));
    };

    // Smooth scroll - Respects accessibility preferences
    const initScroll = () => {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (!link) return;

            const target = document.getElementById(link.getAttribute('href').substring(1));
            if (!target) return;

            e.preventDefault();
            target.scrollIntoView({
                behavior: window.matchMedia('(prefers-reduced-motion: reduce)').matches ? 'auto' : 'smooth'
            });
        });
    };

    // Skip links for accessibility
    const initA11y = () => {
        document.addEventListener('click', (e) => {
            if (!e.target.classList.contains('skip-link')) return;
            
            const target = document.getElementById(e.target.getAttribute('href').substring(1));
            if (target) {
                target.focus();
                target.scrollIntoView();
            }
        });
    };

    // Initialize - Minimal execution time
    ready(() => {
        initImages();
        initScroll();
        initA11y();
        document.body.classList.add('js-ready');
    });

})();
