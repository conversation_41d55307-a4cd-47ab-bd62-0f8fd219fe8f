<?php
/**
 * GP Discover Pro Performance - Core Web Vitals Optimized
 *
 * Performance-first WordPress child theme functions
 * Target: LCP <2.5s | FID <100ms | CLS <0.1
 *
 * @package GP_Discover_Pro_Performance
 * @version 2.1.0
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Constants - Performance First
 */
define('GP_DISCOVER_VERSION', '2.1.0');
define('GP_DISCOVER_PATH', get_stylesheet_directory());
define('GP_DISCOVER_URL', get_stylesheet_directory_uri());

/**
 * Core Web Vitals Critical Setup
 */
add_action('after_setup_theme', 'gp_discover_performance_setup');
function gp_discover_performance_setup() {
    // Essential theme supports only
    add_theme_support('html5', ['search-form', 'comment-form', 'gallery', 'caption']);
    add_theme_support('post-thumbnails');
    add_theme_support('responsive-embeds');

    // Performance-optimized image sizes
    add_image_size('card-thumb', 400, 225, true); // 16:9 aspect ratio
    add_image_size('hero-image', 1200, 675, true); // 16:9 aspect ratio
}

/**
 * Core Web Vitals Performance Optimizations
 */
add_action('wp_enqueue_scripts', 'gp_discover_performance_optimizations');
function gp_discover_performance_optimizations() {
    // Remove performance-heavy WordPress defaults
    wp_dequeue_style('wp-block-library');
    wp_dequeue_style('wp-block-library-theme');
    wp_dequeue_style('wc-blocks-style');
    wp_dequeue_style('classic-theme-styles');

    // Remove jQuery if not needed (improves FID)
    if (!is_admin()) {
        wp_deregister_script('jquery');
    }

    // Minimal JavaScript - only if needed
    if (is_single() || is_page()) {
        wp_enqueue_script(
            'gp-discover-minimal',
            GP_DISCOVER_URL . '/assets/js/minimal.js',
            [],
            GP_DISCOVER_VERSION,
            ['strategy' => 'defer', 'in_footer' => true]
        );
    }
}

/**
 * Critical CSS Inlining - LCP Optimization
 */
add_action('wp_head', 'gp_discover_critical_css', 1);
function gp_discover_critical_css() {
    // Ultra-minimal critical CSS for above-the-fold content
    $critical_css = '
    :root{--primary:#4f46e5;--text:#111827;--bg:#ffffff;--border:#e5e7eb;--font:system-ui,-apple-system,sans-serif}
    *,*::before,*::after{box-sizing:border-box}*{margin:0;padding:0}
    html{font-family:var(--font);font-size:100%;line-height:1.5}
    body{background:var(--bg);color:var(--text);font-size:1rem;overflow-x:hidden}
    .container{max-width:1200px;margin:0 auto;padding:0 1rem}
    .main-navigation{background:var(--bg);border-bottom:1px solid var(--border);position:sticky;top:0;z-index:1000}
    .main-navigation a{color:var(--text);text-decoration:none;padding:0.75rem 1rem;display:inline-block;min-height:44px}
    .card{background:var(--bg);border:1px solid var(--border);border-radius:0.5rem;overflow:hidden}
    .card img{width:100%;height:auto;display:block;aspect-ratio:16/9;object-fit:cover}
    ';

    echo '<style id="gp-discover-critical">' . $critical_css . '</style>';
}

/**
 * Resource Hints - LCP Optimization
 */
add_action('wp_head', 'gp_discover_resource_hints', 2);
function gp_discover_resource_hints() {
    // Preload critical resources
    echo '<link rel="preload" href="' . GP_DISCOVER_URL . '/style.css" as="style">';

    // DNS prefetch for external resources
    echo '<link rel="dns-prefetch" href="//fonts.gstatic.com">';
    echo '<link rel="dns-prefetch" href="//www.google-analytics.com">';

    // Preconnect for critical third-party domains
    echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>';
}

/**
 * Performance-First Search Title Shortcode
 */
add_shortcode('search_title', 'gp_discover_search_title');
function gp_discover_search_title() {
    if (is_search()) {
        $query = get_search_query();
        $count = $GLOBALS['wp_query']->found_posts;
        return '<h1>Search results for: <span>' . esc_html($query) . '</span></h1><p>' . $count . ' results found</p>';
    } elseif (is_archive()) {
        return '<h1>' . get_the_archive_title() . '</h1>';
    }
    return '';
}

/**
 * Skip Links for Accessibility
 */
add_action('wp_body_open', 'gp_discover_skip_links');
function gp_discover_skip_links() {
    echo '<a class="skip-link" href="#main">Skip to main content</a>';
}

/**
 * Image Optimization - CLS Prevention
 */
add_filter('wp_get_attachment_image_attributes', 'gp_discover_image_attributes', 10, 3);
function gp_discover_image_attributes($attr, $attachment, $size) {
    if (!is_admin()) {
        $attr['loading'] = 'lazy';
        $attr['decoding'] = 'async';

        // Add explicit dimensions to prevent CLS
        if ($size === 'card-thumb') {
            $attr['width'] = '400';
            $attr['height'] = '225';
        }
    }
    return $attr;
}

/**
 * Remove Performance-Heavy WordPress Features
 */
add_action('init', 'gp_discover_remove_bloat');
function gp_discover_remove_bloat() {
    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');

    // Disable emojis (improves performance)
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');

    // Remove unnecessary REST API links
    remove_action('wp_head', 'rest_output_link_wp_head');
    remove_action('wp_head', 'wp_oembed_add_discovery_links');
}

/**
 * Minimal Structured Data for SEO
 */
add_action('wp_head', 'gp_discover_structured_data');
function gp_discover_structured_data() {
    if (is_single() && get_post_type() === 'post') {
        $post = get_post();
        $author = get_userdata($post->post_author);

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => get_the_title(),
            'author' => [
                '@type' => 'Person',
                'name' => $author->display_name
            ],
            'datePublished' => get_the_date('c'),
            'dateModified' => get_the_modified_date('c')
        ];

        echo '<script type="application/ld+json">' . wp_json_encode($schema) . '</script>';
    }
}

/**
 * Minimal Social Sharing - Performance Optimized
 */
add_action('wp_footer', 'gp_discover_social_sharing');
function gp_discover_social_sharing() {
    if (is_single()) {
        echo '<script>
        document.addEventListener("DOMContentLoaded", function() {
            const shareButtons = document.querySelectorAll(".social-share a");
            shareButtons.forEach(button => {
                button.addEventListener("click", function(e) {
                    e.preventDefault();
                    const url = this.href;
                    window.open(url, "share", "width=600,height=400");
                });
            });
        });
        </script>';
    }
}

/**
 * Performance Body Classes
 */
add_filter('body_class', 'gp_discover_body_classes');
function gp_discover_body_classes($classes) {
    $classes[] = 'performance-optimized';
    $classes[] = 'core-web-vitals-ready';

    if (wp_is_mobile()) {
        $classes[] = 'mobile-device';
    }

    return $classes;
}

/**
 * Security Headers
 */
add_action('send_headers', 'gp_discover_security_headers');
function gp_discover_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }
}

// End of performance-optimized functions