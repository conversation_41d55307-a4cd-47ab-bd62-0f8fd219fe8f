<?php
/**
 * GP Discover Pro Ultra - Child Theme Functions
 *
 * Ultra-modern, high-performance WordPress child theme functions
 * Optimized for Core Web Vitals, accessibility, and modern web standards
 *
 * @package GP_Discover_Pro_Ultra
 * @version 2.0.0
 * <AUTHOR>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Constants
 */
define('GP_DISCOVER_ULTRA_VERSION', '2.0.0');
define('GP_DISCOVER_ULTRA_PATH', get_stylesheet_directory());
define('GP_DISCOVER_ULTRA_URL', get_stylesheet_directory_uri());

/**
 * Theme Setup and Initialization
 */
add_action('after_setup_theme', 'gp_discover_ultra_setup');
function gp_discover_ultra_setup() {
    // Add theme support for modern features
    add_theme_support('html5', [
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script'
    ]);

    // Add responsive embeds support
    add_theme_support('responsive-embeds');

    // Add custom logo support
    add_theme_support('custom-logo', [
        'height'      => 100,
        'width'       => 400,
        'flex-height' => true,
        'flex-width'  => true,
    ]);

    // Add post thumbnails support
    add_theme_support('post-thumbnails');

    // Add custom image sizes for performance
    add_image_size('gp-discover-card', 400, 250, true);
    add_image_size('gp-discover-hero', 1200, 600, true);
}

/**
 * Performance Optimizations and Script Enqueuing
 */
add_action('wp_enqueue_scripts', 'gp_discover_ultra_performance_optimizations');
function gp_discover_ultra_performance_optimizations() {
    // Remove unnecessary scripts and styles
    wp_dequeue_style('wp-block-library');
    wp_dequeue_style('wp-block-library-theme');
    wp_dequeue_style('wc-blocks-style');

    // Enqueue modern JavaScript with proper loading
    wp_enqueue_script(
        'gp-discover-ultra-theme',
        GP_DISCOVER_ULTRA_URL . '/assets/js/theme.js',
        [],
        GP_DISCOVER_ULTRA_VERSION,
        true // Load in footer
    );

    // Add async/defer attributes for better performance
    add_filter('script_loader_tag', function($tag, $handle) {
        if ('gp-discover-ultra-theme' === $handle) {
            return str_replace(' src', ' async defer src', $tag);
        }
        return $tag;
    }, 10, 2);

    // Add preload hints for critical resources
    add_action('wp_head', function() {
        echo '<link rel="preload" href="' . GP_DISCOVER_ULTRA_URL . '/style.css" as="style">';
        echo '<link rel="preload" href="' . GP_DISCOVER_ULTRA_URL . '/assets/js/theme.js" as="script">';
        echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>';
        echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">';
    }, 1);
}

/**
 * Critical CSS Inlining for Above-the-Fold Performance
 */
add_action('wp_head', 'gp_discover_ultra_inline_critical_css', 1);
function gp_discover_ultra_inline_critical_css() {
    $critical_css = '
    :root{--color-primary-500:oklch(0.65 0.15 280);--color-neutral-0:oklch(1.00 0.00 0);--color-neutral-900:oklch(0.13 0.00 0);--font-system:system-ui,-apple-system,"Segoe UI",Roboto,"Helvetica Neue","Noto Sans","Liberation Sans",Arial,sans-serif;--text-base:clamp(1rem,0.9rem + 0.5vw,1.125rem);--space-4:1rem;--radius-lg:0.75rem;--shadow-base:0 1px 3px 0 rgb(0 0 0 / 0.1),0 1px 2px -1px rgb(0 0 0 / 0.1);--duration-base:250ms;--ease-out:cubic-bezier(0,0,0.2,1)}
    *,*::before,*::after{box-sizing:border-box;margin:0;padding:0}
    html{font-family:var(--font-system);font-size:100%;line-height:1.5;-webkit-text-size-adjust:100%;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
    body{background-color:var(--color-neutral-0);color:var(--color-neutral-900);font-size:var(--text-base);min-height:100vh;overflow-x:hidden}
    .container{max-width:75rem;margin-inline:auto;padding-inline:var(--space-4)}
    .main-navigation{background:rgba(255,255,255,0.95);backdrop-filter:blur(20px);-webkit-backdrop-filter:blur(20px);border-bottom:1px solid rgba(255,255,255,0.2);box-shadow:var(--shadow-base);position:sticky;top:0;z-index:1020}
    ';

    echo '<style id="gp-discover-critical-css">' . $critical_css . '</style>';
}

/**
 * Enhanced Search Title Shortcode with Schema Markup
 */
add_shortcode('search_title', 'gp_discover_ultra_search_title');
function gp_discover_ultra_search_title() {
    $output = '';

    if (is_search()) {
        $search_query = get_search_query();
        $results_count = $GLOBALS['wp_query']->found_posts;

        $output = sprintf(
            '<div class="search-results-header" itemscope itemtype="https://schema.org/SearchResultsPage">
                <h1 class="search-for">Search results for:</h1>
                <h1 class="search-title" itemprop="name">%s</h1>
                <p class="search-results-count" itemprop="numberOfItems">%d results found</p>
            </div>',
            esc_html($search_query),
            $results_count
        );
    } elseif (is_archive()) {
        $archive_title = get_the_archive_title();
        $archive_description = get_the_archive_description();

        $output = sprintf(
            '<div class="archive-header" itemscope itemtype="https://schema.org/CollectionPage">
                <h1 class="archive-title" itemprop="name">%s</h1>
                %s
            </div>',
            $archive_title,
            $archive_description ? '<div class="archive-description" itemprop="description">' . $archive_description . '</div>' : ''
        );
    }

    return $output;
}

/**
 * Add Skip Links for Accessibility
 */
add_action('wp_body_open', 'gp_discover_ultra_skip_links');
function gp_discover_ultra_skip_links() {
    echo '<a class="skip-link sr-only" href="#main">Skip to main content</a>';
    echo '<a class="skip-link sr-only" href="#sidebar">Skip to sidebar</a>';
}

/**
 * Enhanced Image Lazy Loading with Intersection Observer
 */
add_filter('wp_get_attachment_image_attributes', 'gp_discover_ultra_lazy_load_images', 10, 3);
function gp_discover_ultra_lazy_load_images($attr, $attachment, $size) {
    if (!is_admin() && !wp_is_mobile()) {
        $attr['loading'] = 'lazy';
        $attr['decoding'] = 'async';

        // Add intersection observer class for enhanced lazy loading
        $attr['class'] = isset($attr['class']) ? $attr['class'] . ' lazy-load' : 'lazy-load';
    }

    return $attr;
}

/**
 * Add Structured Data for Better SEO
 */
add_action('wp_head', 'gp_discover_ultra_structured_data');
function gp_discover_ultra_structured_data() {
    if (is_single() && get_post_type() === 'post') {
        $post = get_post();
        $author = get_userdata($post->post_author);
        $featured_image = get_the_post_thumbnail_url($post->ID, 'full');

        $structured_data = [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => get_the_title(),
            'description' => get_the_excerpt(),
            'author' => [
                '@type' => 'Person',
                'name' => $author->display_name,
                'url' => get_author_posts_url($author->ID)
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => get_site_icon_url()
                ]
            ],
            'datePublished' => get_the_date('c'),
            'dateModified' => get_the_modified_date('c'),
            'mainEntityOfPage' => get_permalink(),
        ];

        if ($featured_image) {
            $structured_data['image'] = [
                '@type' => 'ImageObject',
                'url' => $featured_image,
                'width' => 1200,
                'height' => 630
            ];
        }

        echo '<script type="application/ld+json">' . wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES) . '</script>';
    }
}
/**
 * Modern Social Sharing Functionality
 */
add_action('wp_footer', 'gp_discover_ultra_social_sharing_script');
function gp_discover_ultra_social_sharing_script() {
    if (is_single()) {
        ?>
        <script>
        // Modern Social Sharing with Web Share API fallback
        document.addEventListener('DOMContentLoaded', function() {
            const shareButtons = document.querySelectorAll('.social-share__button');
            const postTitle = document.title;
            const postUrl = window.location.href;

            shareButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    const platform = this.dataset.platform;

                    // Use Web Share API if available
                    if (navigator.share && platform === 'native') {
                        navigator.share({
                            title: postTitle,
                            url: postUrl
                        }).catch(console.error);
                        return;
                    }

                    // Fallback to traditional sharing
                    let shareUrl = '';
                    switch(platform) {
                        case 'facebook':
                            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(postUrl)}`;
                            break;
                        case 'twitter':
                            shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(postUrl)}&text=${encodeURIComponent(postTitle)}`;
                            break;
                        case 'whatsapp':
                            shareUrl = `https://wa.me/?text=${encodeURIComponent(postTitle + ' ' + postUrl)}`;
                            break;
                        case 'telegram':
                            shareUrl = `https://t.me/share/url?url=${encodeURIComponent(postUrl)}&text=${encodeURIComponent(postTitle)}`;
                            break;
                        case 'linkedin':
                            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(postUrl)}`;
                            break;
                    }

                    if (shareUrl) {
                        window.open(shareUrl, 'share', 'width=600,height=400,scrollbars=yes,resizable=yes');
                    }
                });
            });
        });
        </script>
        <?php
    }
}

/**
 * Enhanced Image Optimization and WebP Support
 */
add_filter('wp_generate_attachment_metadata', 'gp_discover_ultra_generate_webp_images', 10, 2);
function gp_discover_ultra_generate_webp_images($metadata, $attachment_id) {
    if (!function_exists('imagewebp')) {
        return $metadata;
    }

    $file = get_attached_file($attachment_id);
    $info = pathinfo($file);

    if (in_array(strtolower($info['extension']), ['jpg', 'jpeg', 'png'])) {
        $webp_file = $info['dirname'] . '/' . $info['filename'] . '.webp';

        // Create WebP version
        $image = null;
        switch (strtolower($info['extension'])) {
            case 'jpg':
            case 'jpeg':
                $image = imagecreatefromjpeg($file);
                break;
            case 'png':
                $image = imagecreatefrompng($file);
                break;
        }

        if ($image) {
            imagewebp($image, $webp_file, 85);
            imagedestroy($image);
        }
    }

    return $metadata;
}

/**
 * Add Resource Hints for Performance
 */
add_action('wp_head', 'gp_discover_ultra_resource_hints', 2);
function gp_discover_ultra_resource_hints() {
    // Preconnect to external domains
    echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>';
    echo '<link rel="dns-prefetch" href="//www.google-analytics.com">';
    echo '<link rel="dns-prefetch" href="//www.googletagmanager.com">';

    // Preload critical resources
    if (is_front_page()) {
        $hero_image = get_theme_mod('hero_background_image');
        if ($hero_image) {
            echo '<link rel="preload" href="' . esc_url($hero_image) . '" as="image">';
        }
    }
}

/**
 * Modern Table of Contents with Smooth Scrolling
 */
add_shortcode('toc', 'gp_discover_ultra_table_of_contents');
function gp_discover_ultra_table_of_contents($atts) {
    $atts = shortcode_atts([
        'depth' => 3,
        'title' => 'Table of Contents'
    ], $atts);

    global $post;
    if (!$post) return '';

    $content = $post->post_content;
    $headings = [];

    // Extract headings
    preg_match_all('/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/i', $content, $matches, PREG_SET_ORDER);

    foreach ($matches as $match) {
        $level = intval($match[1]);
        if ($level <= $atts['depth']) {
            $text = strip_tags($match[2]);
            $id = sanitize_title($text);
            $headings[] = [
                'level' => $level,
                'text' => $text,
                'id' => $id
            ];
        }
    }

    if (empty($headings)) return '';

    $output = '<div class="toc" id="table-of-contents">';
    $output .= '<div class="toc__header">' . esc_html($atts['title']) . '</div>';
    $output .= '<ul class="toc__list">';

    foreach ($headings as $heading) {
        $output .= sprintf(
            '<li class="toc__item toc__item--level-%d">
                <a href="#%s" class="toc__link">%s</a>
            </li>',
            $heading['level'],
            $heading['id'],
            esc_html($heading['text'])
        );
    }

    $output .= '</ul></div>';

    // Add smooth scrolling script
    $output .= '<script>
    document.addEventListener("DOMContentLoaded", function() {
        const tocLinks = document.querySelectorAll(".toc__link");
        tocLinks.forEach(link => {
            link.addEventListener("click", function(e) {
                e.preventDefault();
                const targetId = this.getAttribute("href").substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: "smooth",
                        block: "start"
                    });
                }
            });
        });
    });
    </script>';

    return $output;
}

/**
 * Add Custom Body Classes for Enhanced Styling
 */
add_filter('body_class', 'gp_discover_ultra_body_classes');
function gp_discover_ultra_body_classes($classes) {
    // Add browser detection classes
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    if (strpos($user_agent, 'Chrome') !== false) {
        $classes[] = 'browser-chrome';
    } elseif (strpos($user_agent, 'Firefox') !== false) {
        $classes[] = 'browser-firefox';
    } elseif (strpos($user_agent, 'Safari') !== false) {
        $classes[] = 'browser-safari';
    }

    // Add device type classes
    if (wp_is_mobile()) {
        $classes[] = 'device-mobile';
    } else {
        $classes[] = 'device-desktop';
    }

    // Add performance classes
    $classes[] = 'performance-optimized';
    $classes[] = 'accessibility-ready';

    return $classes;
}

/**
 * Optimize WordPress Admin for Better Performance
 */
add_action('admin_init', 'gp_discover_ultra_admin_optimizations');
function gp_discover_ultra_admin_optimizations() {
    // Remove unnecessary admin features for better performance
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');

    // Disable emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('admin_print_styles', 'print_emoji_styles');
}

/**
 * Add Security Headers for Better Protection
 */
add_action('send_headers', 'gp_discover_ultra_security_headers');
function gp_discover_ultra_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Permissions-Policy: camera=(), microphone=(), geolocation=()');
    }
}

/**
 * Theme Customizer Enhancements
 */
add_action('customize_register', 'gp_discover_ultra_customizer');
function gp_discover_ultra_customizer($wp_customize) {
    // Add Performance Section
    $wp_customize->add_section('gp_discover_performance', [
        'title' => 'Performance Settings',
        'priority' => 30,
        'description' => 'Configure performance optimization settings'
    ]);

    // Enable/Disable WebP Images
    $wp_customize->add_setting('enable_webp_images', [
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean'
    ]);

    $wp_customize->add_control('enable_webp_images', [
        'label' => 'Enable WebP Image Generation',
        'section' => 'gp_discover_performance',
        'type' => 'checkbox'
    ]);

    // Enable/Disable Critical CSS
    $wp_customize->add_setting('enable_critical_css', [
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean'
    ]);

    $wp_customize->add_control('enable_critical_css', [
        'label' => 'Enable Critical CSS Inlining',
        'section' => 'gp_discover_performance',
        'type' => 'checkbox'
    ]);
}

/**
 * Initialize Theme Features
 */
add_action('init', 'gp_discover_ultra_init');
function gp_discover_ultra_init() {
    // Add support for editor styles
    add_theme_support('editor-styles');
    add_editor_style('style.css');

    // Add support for wide and full alignment
    add_theme_support('align-wide');

    // Add support for block editor color palette
    add_theme_support('editor-color-palette', [
        [
            'name' => 'Primary',
            'slug' => 'primary',
            'color' => '#6366f1',
        ],
        [
            'name' => 'Secondary',
            'slug' => 'secondary',
            'color' => '#64748b',
        ],
        [
            'name' => 'Success',
            'slug' => 'success',
            'color' => '#10b981',
        ],
        [
            'name' => 'Warning',
            'slug' => 'warning',
            'color' => '#f59e0b',
        ],
        [
            'name' => 'Error',
            'slug' => 'error',
            'color' => '#ef4444',
        ]
    ]);
}

// End of functions.php