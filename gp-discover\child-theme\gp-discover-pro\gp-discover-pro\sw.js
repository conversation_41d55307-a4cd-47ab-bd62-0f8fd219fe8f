/**
 * GP Discover Pro Ultra - Service Worker
 * 
 * Modern service worker for PWA functionality and performance optimization
 * Features: Caching strategies, offline support, background sync
 * 
 * @version 2.0.0
 * <AUTHOR>
 */

const CACHE_NAME = 'gp-discover-ultra-v2.0.0';
const STATIC_CACHE = 'gp-discover-static-v2.0.0';
const DYNAMIC_CACHE = 'gp-discover-dynamic-v2.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
    '/',
    '/wp-content/themes/gp-discover-pro-ultra/style.css',
    '/wp-content/themes/gp-discover-pro-ultra/assets/js/theme.js',
    '/wp-includes/css/dist/block-library/style.min.css',
    '/wp-includes/js/wp-emoji-release.min.js'
];

// Cache strategies for different content types
const CACHE_STRATEGIES = {
    // Cache first for static assets
    static: [
        /\.(?:css|js|woff2?|ttf|eot)$/,
        /\/wp-content\/themes\//,
        /\/wp-includes\//
    ],
    
    // Network first for dynamic content
    dynamic: [
        /\/wp-admin\//,
        /\/wp-json\//,
        /\?/
    ],
    
    // Stale while revalidate for images
    images: [
        /\.(?:png|jpg|jpeg|svg|gif|webp|avif)$/
    ]
};

/**
 * Install Event - Cache static assets
 */
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Service Worker: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Service Worker: Static assets cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Failed to cache static assets', error);
            })
    );
});

/**
 * Activate Event - Clean up old caches
 */
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && 
                            cacheName !== DYNAMIC_CACHE && 
                            cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated');
                return self.clients.claim();
            })
    );
});

/**
 * Fetch Event - Implement caching strategies
 */
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip cross-origin requests (unless they're assets)
    if (url.origin !== location.origin && !isAssetRequest(request)) {
        return;
    }
    
    // Determine cache strategy
    const strategy = getCacheStrategy(request);
    
    switch (strategy) {
        case 'static':
            event.respondWith(cacheFirst(request));
            break;
        case 'dynamic':
            event.respondWith(networkFirst(request));
            break;
        case 'images':
            event.respondWith(staleWhileRevalidate(request));
            break;
        default:
            event.respondWith(networkFirst(request));
    }
});

/**
 * Cache First Strategy - For static assets
 */
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Cache First failed:', error);
        return new Response('Offline', { status: 503 });
    }
}

/**
 * Network First Strategy - For dynamic content
 */
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed, trying cache:', error);
        
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline page for navigation requests
        if (request.mode === 'navigate') {
            return caches.match('/offline.html') || 
                   new Response('Offline', { status: 503 });
        }
        
        return new Response('Offline', { status: 503 });
    }
}

/**
 * Stale While Revalidate Strategy - For images
 */
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(() => cachedResponse);
    
    return cachedResponse || fetchPromise;
}

/**
 * Determine cache strategy based on request
 */
function getCacheStrategy(request) {
    const url = request.url;
    
    // Check static assets
    for (const pattern of CACHE_STRATEGIES.static) {
        if (pattern.test(url)) {
            return 'static';
        }
    }
    
    // Check images
    for (const pattern of CACHE_STRATEGIES.images) {
        if (pattern.test(url)) {
            return 'images';
        }
    }
    
    // Check dynamic content
    for (const pattern of CACHE_STRATEGIES.dynamic) {
        if (pattern.test(url)) {
            return 'dynamic';
        }
    }
    
    return 'dynamic'; // Default strategy
}

/**
 * Check if request is for an asset
 */
function isAssetRequest(request) {
    const url = request.url;
    return /\.(css|js|png|jpg|jpeg|gif|svg|woff2?|ttf|eot|webp|avif)$/.test(url);
}

/**
 * Background Sync for form submissions
 */
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    try {
        // Handle queued form submissions or other background tasks
        console.log('Background sync triggered');
        
        // Example: Send queued analytics data
        const queuedData = await getQueuedData();
        if (queuedData.length > 0) {
            await sendQueuedData(queuedData);
            await clearQueuedData();
        }
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

/**
 * Push notification handling
 */
self.addEventListener('push', event => {
    if (!event.data) return;
    
    const data = event.data.json();
    const options = {
        body: data.body,
        icon: '/wp-content/themes/gp-discover-pro-ultra/assets/images/icon-192x192.png',
        badge: '/wp-content/themes/gp-discover-pro-ultra/assets/images/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: data.primaryKey
        },
        actions: [
            {
                action: 'explore',
                title: 'Read More',
                icon: '/wp-content/themes/gp-discover-pro-ultra/assets/images/checkmark.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/wp-content/themes/gp-discover-pro-ultra/assets/images/xmark.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification(data.title, options)
    );
});

/**
 * Notification click handling
 */
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow(event.notification.data.url || '/')
        );
    }
});

/**
 * Helper functions for background sync
 */
async function getQueuedData() {
    // Implementation depends on your storage strategy
    return [];
}

async function sendQueuedData(data) {
    // Send data to server
    console.log('Sending queued data:', data);
}

async function clearQueuedData() {
    // Clear queued data after successful send
    console.log('Clearing queued data');
}

/**
 * Cache size management
 */
async function limitCacheSize(cacheName, maxItems) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    
    if (keys.length > maxItems) {
        const keysToDelete = keys.slice(0, keys.length - maxItems);
        await Promise.all(keysToDelete.map(key => cache.delete(key)));
    }
}

// Periodically clean up caches
setInterval(() => {
    limitCacheSize(DYNAMIC_CACHE, 50);
}, 60000); // Every minute

console.log('Service Worker: Loaded successfully! 🚀');
